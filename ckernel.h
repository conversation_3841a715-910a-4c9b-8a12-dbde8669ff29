#ifndef CKERNEL_H
#define CKERNEL_H

#include <QObject>
#include "wechatdialog.h"
#include "TcpClientMediator.h"
#include "packdef.h"
//登录头文件
#include "logindialog.h"
#include "roomdialog.h"
#include "audioread.h"
#include "audiowrite.h"

//协议映射表使用的类型
class Ckernel;
typedef void (Ckernel::*PFUN)(uint sock,char* buf,int nlen);


class Ckernel : public QObject
{
    Q_OBJECT
public:
    explicit Ckernel(QObject *parent = nullptr);

    //单例 (不建议写这种单例，没法回收，后面如有时间，改一改)
    static Ckernel* GetInstance()
    {
        static Ckernel kernel;
        return & kernel;

    }

signals:

public slots:
    void setNetPackMap();

    //初始化配置
    void initConfig();
    void slot_destroy();//回收
    //发送登录信息
    void slot_loginCommit(QString tel,QString pass);
    //发送注册信息
    void slot_registerCommit(QString name,QString tel,QString pass);



    //网络信息处理
    void slot_dealData(uint sock,char* buf,int nlen);
    //登录回复
    void slot_dealLoginRs(uint sock,char* buf,int nlen);
    //注册回复
    void slot_dealRegisterRs(uint sock,char* buf,int nlen);
    //创建房间&加入房间&退出房间
    void slot_createRoom();
    void slot_joinRoom();
    void slot_quitRoom();
    //创建房间回复
    void slot_dealCreateRoomRs(uint sock,char* buf,int nlen);
    //加入房间回复处理
    void slot_dealJoinCRoomRs(uint sock,char* buf,int nlen);
    //房间成员请求回复处理
    void slot_dealRoomMemberRq(uint sock,char* buf,int nlen);
    //离开房间的请求处理
    void slot_dealLeaveRoomRq(uint sock,char* buf,int nlen);
    //开启音频
    void slot_startAudio(uint sock,char* buf,int nlen);
    //关闭音频
    void slot_pauseAudio(uint sock,char* buf,int nlen);
    //发送音频帧
    void slot_audioFrame(QByteArray ba);
    //音频帧处理
    void slot_dealAudioFrameRq(uint sock,char* buf,int nlen);


//私有成员
private:
    WeChatDialog * m_pWeChatDlg;
    LoginDialog * m_pLoginDlg;//登录窗口
    INetMediator * m_pClient;//客户端中介者类
    RoomDialog* m_pRoomdialog;//房间窗口


    PFUN m_netPackMap[_DEF_PACK_COUNT];    //协议映射表
    std::map<int,UserShow*> m_mapIDToUserShow;//id映射到usershow上

    QString m_serverIP;
    QString m_name;//用户姓名
    int m_id;//用户id
    int m_roomid;//房间号

    //音频 一个采集 多个播放 每一个房间成员 1：1 map映射
    AudioRead* m_pAudioRead;
    std::map<int,AudioWrite*> m_mapIDToAudioWrite;


};

#endif // CKERNEL_H
