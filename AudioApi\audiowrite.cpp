#include "audiowrite.h"
#include <QDebug>

AudioWrite::AudioWrite(QObject *parent): QObject{parent}
{
    // 初始化成员变量
    m_audio_out = nullptr;
    m_buffer_out = nullptr;

    try {
        //speex 初始化
        speex_bits_init(&bits_dec);
        Dec_State = speex_decoder_init(speex_lib_get_mode(SPEEX_MODEID_NB));
        if (!Dec_State) {
            qDebug() << "Speex decoder initialization failed!";
            return;
        }

        //声卡采样格式
        format.setSampleRate(8000);
        format.setChannelCount(1);
        format.setSampleSize(16);
        format.setCodec("audio/pcm");
        format.setByteOrder(QAudioFormat::LittleEndian);
        format.setSampleType(QAudioFormat::SignedInt);  // 修改为SignedInt

        QAudioDeviceInfo info = QAudioDeviceInfo::defaultOutputDevice();  // 修改为输出设备
        if (!info.isNull()) {
            if (!info.isFormatSupported(format)) {
                qDebug() << "Audio output format not supported, using nearest format";
                format = info.nearestFormat(format);
            }
        } else {
            qDebug() << "No default audio output device found!";
            QMessageBox::warning(nullptr, "警告", "未找到音频输出设备！");
            return;
        }

        m_audio_out = new QAudioOutput(format, this);
        if (m_audio_out) {
            //向buffer里面存数据，就会触发播放声音
            m_buffer_out = m_audio_out->start();
        }

        qDebug() << "AudioWrite initialized successfully";
    } catch (...) {
        qDebug() << "Exception occurred during AudioWrite initialization";
        QMessageBox::critical(nullptr, "错误", "音频播放模块初始化失败！");
    }
}

AudioWrite::~AudioWrite()
{
    // 停止音频播放
    if (m_audio_out) {
        m_audio_out->stop();
        delete m_audio_out;
        m_audio_out = nullptr;
    }

    // 清理Speex资源
    if (Dec_State) {
        speex_decoder_destroy(Dec_State);
        Dec_State = nullptr;
    }
    speex_bits_destroy(&bits_dec);

    qDebug() << "AudioWrite destroyed";
}

void AudioWrite::slot_playAudio(QByteArray bt)
{
#ifdef USE_SPEEX
    char bytes[800] = {0};
    int i = 0;
    float output_frame1[320] = {0};
    char buf[800] = {0};
    memcpy(bytes,bt.data(),bt.length() / 2);
    //解压缩数据 106 62
    //speex_bits_reset(&bits_dec);
    speex_bits_read_from(&bits_dec,bytes,bt.length() / 2);
    int error = speex_decode(Dec_State,&bits_dec,output_frame1);
    //将解压后数据转换为声卡识别格式
    //大端
    short num = 0;
    for (i = 0;i < 160;i++)
    {
        num = output_frame1[i];
        buf[2 * i] = num;
        buf[2 * i + 1] = num >> 8;
        //qDebug() << "float out" << num << output_frame1[i];
    }
    memcpy(bytes,bt.data() + bt.length() / 2,bt.length() / 2);
    //解压缩数据
    //speex_bits_reset(&bits_dec);
    speex_bits_read_from(&bits_dec,bytes,bt.length() / 2);
    error = speex_decode(Dec_State,&bits_dec,output_frame1);
    //将解压后数据转换为声卡识别格式
    //大端 -> 小端
    for (i = 0;i < 160;i++)
    {
        num = output_frame1[i];
        buf[2 * i + 320] = num;
        buf[2 * i + 1 + 320] = num >> 8;
    }
    m_buffer_out->write(buf,640);
    return;
#else
    if(bt.size()<640) return ;
    m_buffer_out->write( bt.data() , 640 );
#endif
}
