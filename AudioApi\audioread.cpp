#include "audioread.h"
#include <QDebug>

//  数据量 40ms 640字节
//  1s的数据量 640*25 = 16000byte 16k

//speex 窄带模式 8kHz 20ms 采样点 160 每一个点是float类型进行编码 操作320字节

AudioRead::AudioRead(QObject *parent) : QObject{parent}
{
    // 初始化成员变量
    m_audio_in = nullptr;
    m_buffer_in = nullptr;
    m_audioState = stopped;

    try {
        //speex 初始化
        speex_bits_init(&bits_enc);
        Enc_State = speex_encoder_init(speex_lib_get_mode(SPEEX_MODEID_NB));
        if (!Enc_State) {
            qDebug() << "Speex encoder initialization failed!";
            return;
        }

        //设置压缩质量
        //#define SPEEX_QUALITY (8)
        //设置质量为 8(15kbps)
        int tmp = SPEEX_QUALITY;
        speex_encoder_ctl(Enc_State,SPEEX_SET_QUALITY,&tmp);

        //声卡采样格式 采样率:8kHZ 位宽:16位 单声道
        format.setSampleRate(8000);
        format.setChannelCount(1);
        format.setSampleSize(16);
        format.setCodec("audio/pcm");
        format.setByteOrder(QAudioFormat::LittleEndian);
        format.setSampleType(QAudioFormat::SignedInt);  // 修改为SignedInt

        QAudioDeviceInfo info = QAudioDeviceInfo::defaultInputDevice();
        if (!info.isNull()) {
            if (!info.isFormatSupported(format)) {
                qDebug() << "Audio format not supported, using nearest format";
                format = info.nearestFormat(format);
            }
        } else {
            qDebug() << "No default audio input device found!";
            QMessageBox::warning(nullptr, "警告", "未找到音频输入设备！");
            return;
        }

        // 创建定时器
        m_timer = new QTimer(this);
        connect(m_timer,SIGNAL(timeout()),this,SLOT(ReadMore()));

        qDebug() << "AudioRead initialized successfully";
    } catch (...) {
        qDebug() << "Exception occurred during AudioRead initialization";
        QMessageBox::critical(nullptr, "错误", "音频读取模块初始化失败！");
    }
}

AudioRead::~AudioRead()
{
    // 停止音频采集
    if (m_timer && m_timer->isActive()) {
        m_timer->stop();
        delete m_timer;
        m_timer = nullptr;
    }

    if (m_audio_in) {
        m_audio_in->stop();
        delete m_audio_in;
        m_audio_in = nullptr;
    }

    // 清理Speex资源
    if (Enc_State) {
        speex_encoder_destroy(Enc_State);
        Enc_State = nullptr;
    }
    speex_bits_destroy(&bits_enc);

    qDebug() << "AudioRead destroyed";
}

//压缩率 640 -> 76
void AudioRead::ReadMore()
{
#ifdef USE_SPEEX
    if (!m_audio_in)
        return;
    QByteArray m_buffer(2048,0);
    qint64 len = m_audio_in->bytesReady();
    if (len < 640)
    {
        return;
    }
    qint64 l = m_buffer_in->read(m_buffer.data(), 640);
    QByteArray frame;
    //speex 编码
    char bytes[800] = {0};
    int i = 0;
    float input_frame1[320];
    char buf[800] = {0};
    char* pInData = (char*)m_buffer.data() ;
    memcpy( buf , pInData , 640);
    int nbytes = 0;
    {
        //转换为大端 ->float
        short num = 0;
        for (i = 0;i < 160;i++)
        {
            num = (uint8_t)buf[2 * i] | (((uint8_t)buf[2 * i + 1]) << 8);
            input_frame1[i] = num;
            //num = m_buffer[2 * i] | ((short)(m_buffer[2 * i + 1]) << 8);
            //qDebug() << "float in" << num << input_frame1[i];
        }
        //压缩数据
        speex_bits_reset(&bits_enc);
        speex_encode(Enc_State,input_frame1,&bits_enc);
        nbytes = speex_bits_write(&bits_enc,bytes,800);
        frame.append(bytes,nbytes);
        //大端
        for (i = 0;i < 160;i++)
        {
            num = (uint8_t)buf[2 * i + 320] | (((uint8_t)buf[2 * i + 1 + 320]) << 8);
            input_frame1[i] = num;
        }
        //压缩数据
        speex_bits_reset(&bits_enc);
        speex_encode(Enc_State,input_frame1,&bits_enc);
        nbytes = speex_bits_write(&bits_enc,bytes,800);
        frame.append(bytes,nbytes);
        qDebug() << "nbytes = " << frame.size();
        Q_EMIT SIG_audioFrame (frame);
        return;
    }


//    frame.append(m_buffer.data(),640);
//    Q_EMIT SIG_audioFrame( frame );
#else
    if (!m_audio_in)
        return;
    QByteArray m_buffer(2048,0);
    qint64 len = m_audio_in->bytesReady();
    if (len < 640)
    {
        return;
    }
    qint64 l = m_buffer_in->read(m_buffer.data(), 640);
    QByteArray frame;
    frame.append(m_buffer.data(),640);
    Q_EMIT SIG_audioFrame( frame );
#endif
}

void AudioRead::start()
{
    if(m_audioState == stopped || m_audioState == pasuing)
    {
        m_audio_in = new QAudioInput(format,this);
        //返回缓冲区地址给成员
        m_buffer_in = m_audio_in->start();//声音采集开始

        m_timer->start(1000/40);

        m_audioState = playing;
    }
}

void AudioRead::pause()
{
    if(m_audioState == playing)
    {
        m_timer->stop();//关闭定时器
        if(m_audio_in)
        {
            m_audio_in->stop();
            delete m_audio_in;
            m_audio_in = NULL;
        }
        m_audioState = pasuing;
    }
}
